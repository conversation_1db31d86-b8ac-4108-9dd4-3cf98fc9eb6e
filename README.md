
# myOCADU-Cards
myOCAD U/Ellucian Experience Cards provided by Marketing &amp; Communications


## HTML Cards

HTML cards are built using HandlebarsJS, using data provided by Drupal Views API endpoints from the public website. 

All assets (CSS, JS) for cards need to either be included in this single file or externally referenced. Contents of the card files is pasted into the editor within myOCAD U as what is effectively a single page hosted within myOCAD U, displayed as a card in an iFrame.


## SDK Cards

React / Ellucian Experience cards are built using React and the Ellucian Experience SDK (https://github.com/ellucian-developer/experience-sdk-sample-extensions). Currently each discreet card should be it's own "extension" or project, but this may change. To build one of these components, navigate to the card's src directory and run `npm run deploy-dev` and if the linter finds no issues, the extension will be uploaded to the dev/test instance of the portal. To deploy to production, do the same but  `npm run deploy-prod` (this requires a valid + authorized production key in the extensions .env file, see below). 

This repo excludes the .env files for each extension and you will have to create your own before deploying for testing/produciton: 

    EXPERIENCE_EXTENSION_UPLOAD_TOKEN=TOKEN-GOES-HERE-DO-NOT-SHARE-OR-COMMIT
    EXPERIENCE_EXTENSION_ENABLED=true
    #Change the extension enviroment below for Test or Production as needed
    EXPERIENCE_EXTENSION_ENVIRONMENTS=Test

When working in test, the `EXPERIENCE_EXTENSION_ENVIRONMENTS` should be `Test` and in production it should be `Production`. You will also need to use the correct token for the environment you are deploying to; these tokens need to be requested from ITS (Chris Thompson) and are created in the Ellucian Experience administration UI (not the content admin UI, but the system admin UI). 

When deploying to production, you **must** increment the version number. If you need to deploy a build without a version bump, you should do this to dev/test only as it does not have this requirement. The only deployments to production should be tested, "final" builds with appropriate versioning. 

While actively working on cards, you can also use `npm start` to enable LiveReload and see your changes faster, without having to continually rebuild and deploy. This only works for extensions that have already been published, and your mileage may vary on support when working with CustomCardConfiguration (stored data + structure). Best used for front end styling.
    

