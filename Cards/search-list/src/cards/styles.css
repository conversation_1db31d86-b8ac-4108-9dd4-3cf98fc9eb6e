/* global stuff */


@import url("https://use.typekit.net/thk7oyq.css");

.eee-dashboard-card *  {
font-family: "Gotham", Arial, sans-serif !important;
transition: all 0.15s ease-out;
letter-spacing: 0px !important;
}

#search-list-config th div, #search-list-config td div {
width: 100%;
}

/* Ensure consistent styling for list items */
#search-list-config tr {
  background-color: #ffffff;
}

#search-list-config tr:nth-child(4n+3),
#search-list-config tr:nth-child(4n+4) {
  background-color: #f9f9f9;
}

#search-list-config tr:nth-child(4n+4) {
  border-bottom: 2px solid #e0e0e0;
}

/* Remove border between the two rows of each list item */
#search-list-config tr:nth-child(4n+3) {
  border-bottom: none;
}

#search-list-config tr:nth-child(4n+1) {
  border-bottom: none;
}

#search-list-config td {
  vertical-align: top;
}

/* Adjust table layout for better column sizing */
#search-list-config {
  table-layout: fixed;
  width: 100%;
}

#search-list-config th:nth-child(1),
#search-list-config td:nth-child(1) {
  width: 40px;
}

#search-list-config th:nth-child(2),
#search-list-config td:nth-child(2) {
  width: 20%;
}

#search-list-config th:nth-child(3),
#search-list-config td:nth-child(3) {
  width: 25%;
}

#search-list-config th:nth-child(4),
#search-list-config td:nth-child(4) {
  width: 25%;
}

#search-list-config th:nth-child(5),
#search-list-config td:nth-child(5) {
  width: 15%;
}

#search-list-config th:nth-child(6),
#search-list-config td:nth-child(6) {
  width: 60px;
}

/* Center icons in their columns */
#search-list-config td:nth-child(1),
#search-list-config td:nth-child(6) {
  text-align: center;
  vertical-align: middle;
}

/* Add top margin to icons in first and last columns */
#search-list-config td:nth-child(1) .drag-handle,
#search-list-config td:nth-child(6) button {
  margin-top: 16px;
}

button.add-button {
  margin-top: 20px;
}

/* Drag and drop styles */
#search-list-config tr[draggable="true"]:hover {
  background-color: #e3f2fd !important;
  transition: background-color 0.2s ease;
}

#search-list-config tr[draggable="true"]:active {
  opacity: 0.7;
}

#search-list-config .drag-handle {
  cursor: grab;
  user-select: none;
}

#search-list-config .drag-handle:active {
  cursor: grabbing;
}

/* Drop zone indicator */
#search-list-config tr.drag-over {
  background-color: #e3f2fd !important;
  border-top: 3px solid #2196f3 !important;
  border-bottom: 3px solid #2196f3 !important;
}

#search-list-config tr.drag-over + tr {
  border-top: 3px solid #2196f3 !important;
}

/* Being dragged item */
#search-list-config tr[style*="opacity: 0.5"] {
  background-color: #f5f5f5 !important;
}
